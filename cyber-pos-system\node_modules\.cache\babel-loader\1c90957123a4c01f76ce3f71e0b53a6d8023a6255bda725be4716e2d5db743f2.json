{"ast": null, "code": "/**\n * Utility function to remove undefined values from objects\n * This is essential for Firebase operations which don't allow undefined values\n */\nexport const cleanObject = obj => {\n  const cleaned = {};\n  for (const [key, value] of Object.entries(obj)) {\n    if (value !== undefined) {\n      if (typeof value === 'object' && value !== null && !Array.isArray(value) && !(value instanceof Date)) {\n        cleaned[key] = cleanObject(value);\n      } else {\n        cleaned[key] = value;\n      }\n    }\n  }\n  return cleaned;\n};\n\n/**\n * Clean an array of objects, removing undefined values from each object\n */\nexport const cleanObjectArray = arr => {\n  return arr.map(item => cleanObject(item));\n};", "map": {"version": 3, "names": ["cleanObject", "obj", "cleaned", "key", "value", "Object", "entries", "undefined", "Array", "isArray", "Date", "cleanObjectArray", "arr", "map", "item"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/utils/objectUtils.ts"], "sourcesContent": ["/**\n * Utility function to remove undefined values from objects\n * This is essential for Firebase operations which don't allow undefined values\n */\nexport const cleanObject = (obj: any): any => {\n  const cleaned: any = {};\n  for (const [key, value] of Object.entries(obj)) {\n    if (value !== undefined) {\n      if (typeof value === 'object' && value !== null && !Array.isArray(value) && !(value instanceof Date)) {\n        cleaned[key] = cleanObject(value);\n      } else {\n        cleaned[key] = value;\n      }\n    }\n  }\n  return cleaned;\n};\n\n/**\n * Clean an array of objects, removing undefined values from each object\n */\nexport const cleanObjectArray = <T>(arr: T[]): T[] => {\n  return arr.map(item => cleanObject(item));\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,WAAW,GAAIC,GAAQ,IAAU;EAC5C,MAAMC,OAAY,GAAG,CAAC,CAAC;EACvB,KAAK,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACL,GAAG,CAAC,EAAE;IAC9C,IAAIG,KAAK,KAAKG,SAAS,EAAE;MACvB,IAAI,OAAOH,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,IAAI,CAACI,KAAK,CAACC,OAAO,CAACL,KAAK,CAAC,IAAI,EAAEA,KAAK,YAAYM,IAAI,CAAC,EAAE;QACpGR,OAAO,CAACC,GAAG,CAAC,GAAGH,WAAW,CAACI,KAAK,CAAC;MACnC,CAAC,MAAM;QACLF,OAAO,CAACC,GAAG,CAAC,GAAGC,KAAK;MACtB;IACF;EACF;EACA,OAAOF,OAAO;AAChB,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMS,gBAAgB,GAAOC,GAAQ,IAAU;EACpD,OAAOA,GAAG,CAACC,GAAG,CAACC,IAAI,IAAId,WAAW,CAACc,IAAI,CAAC,CAAC;AAC3C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}