{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { collection, doc, addDoc, getDoc, serverTimestamp, runTransaction, writeBatch } from 'firebase/firestore';\nimport { db } from '../config/firebase';\nimport { cleanObject } from '../utils/objectUtils';\nexport const useTransactions = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Create or update customer for credit sales\n  const createOrUpdateCustomer = async (customerName, customerPhone) => {\n    try {\n      // Check if customer already exists by name\n      const customersRef = collection(db, 'customers');\n      const customerData = {\n        name: customerName.trim(),\n        phone: customerPhone === null || customerPhone === void 0 ? void 0 : customerPhone.trim(),\n        totalDebt: 0,\n        isActive: true,\n        createdAt: new Date(),\n        updatedAt: new Date()\n      };\n\n      // For simplicity, create a new customer document\n      // In production, you might want to search for existing customers first\n      const customerDoc = await addDoc(customersRef, {\n        ...customerData,\n        createdAt: serverTimestamp(),\n        updatedAt: serverTimestamp()\n      });\n      return customerDoc.id;\n    } catch (error) {\n      console.error('Error creating customer:', error);\n      throw new Error('Failed to create customer record');\n    }\n  };\n\n  // Update product stock quantities\n  const updateProductStock = async items => {\n    const batch = writeBatch(db);\n    for (const item of items) {\n      if (item.type === 'product') {\n        const productRef = doc(db, 'products', item.itemId);\n        const productDoc = await getDoc(productRef);\n        if (productDoc.exists()) {\n          const currentStock = productDoc.data().stockQuantity || 0;\n          const newStock = Math.max(0, currentStock - item.quantity);\n          batch.update(productRef, {\n            stockQuantity: newStock,\n            updatedAt: serverTimestamp()\n          });\n        }\n      }\n    }\n    await batch.commit();\n  };\n\n  // Save transaction to Firebase\n  const saveTransaction = async (cartState, paymentData, attendantId) => {\n    setLoading(true);\n    setError(null);\n    try {\n      var _paymentData$notes;\n      // Prepare transaction items - cleanObject will remove undefined values\n      const transactionItems = cartState.items.map(item => {\n        var _item$notes;\n        return cleanObject({\n          id: item.id,\n          type: item.type,\n          itemId: item.itemId,\n          name: item.name,\n          quantity: item.quantity,\n          unitPrice: item.unitPrice,\n          totalPrice: item.totalPrice,\n          notes: (_item$notes = item.notes) === null || _item$notes === void 0 ? void 0 : _item$notes.trim()\n        });\n      });\n\n      // Handle customer creation for credit sales\n      let customerId;\n      if (paymentData.paymentMethod === 'debt' && paymentData.customerName) {\n        customerId = await createOrUpdateCustomer(paymentData.customerName, paymentData.customerPhone);\n      }\n\n      // Prepare transaction data - ensure no undefined values\n      const rawTransactionData = {\n        items: transactionItems,\n        subtotal: cartState.subtotal,\n        discount: cartState.discount || 0,\n        total: cartState.total,\n        paymentMethod: paymentData.paymentMethod,\n        customerId,\n        attendantId,\n        status: 'completed',\n        notes: (_paymentData$notes = paymentData.notes) === null || _paymentData$notes === void 0 ? void 0 : _paymentData$notes.trim(),\n        createdAt: new Date(),\n        updatedAt: new Date()\n      };\n\n      // Clean the transaction data to remove any undefined values\n      const transactionData = cleanObject(rawTransactionData);\n\n      // Use Firestore transaction to ensure data consistency\n      const transactionId = await runTransaction(db, async transaction => {\n        // Create the transaction document\n        const transactionRef = doc(collection(db, 'transactions'));\n        transaction.set(transactionRef, {\n          ...transactionData,\n          createdAt: serverTimestamp(),\n          updatedAt: serverTimestamp()\n        });\n\n        // Update customer debt if it's a credit sale\n        if (customerId && paymentData.paymentMethod === 'debt') {\n          const customerRef = doc(db, 'customers', customerId);\n          const customerDoc = await transaction.get(customerRef);\n          if (customerDoc.exists()) {\n            const currentDebt = customerDoc.data().totalDebt || 0;\n            transaction.update(customerRef, {\n              totalDebt: currentDebt + cartState.total,\n              updatedAt: serverTimestamp()\n            });\n          }\n        }\n        return transactionRef.id;\n      });\n\n      // Update product stock (outside of the transaction for better performance)\n      await updateProductStock(transactionItems);\n      return transactionId;\n    } catch (error) {\n      console.error('Error saving transaction:', error);\n      setError('Failed to save transaction');\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n  return {\n    saveTransaction,\n    loading,\n    error\n  };\n};\n_s(useTransactions, \"Iz3ozxQ+abMaAIcGIvU8cKUcBeo=\");", "map": {"version": 3, "names": ["useState", "collection", "doc", "addDoc", "getDoc", "serverTimestamp", "runTransaction", "writeBatch", "db", "cleanObject", "useTransactions", "_s", "loading", "setLoading", "error", "setError", "createOrUpdateCustomer", "customerName", "customerPhone", "customersRef", "customerData", "name", "trim", "phone", "totalDebt", "isActive", "createdAt", "Date", "updatedAt", "customerDoc", "id", "console", "Error", "updateProductStock", "items", "batch", "item", "type", "productRef", "itemId", "productDoc", "exists", "currentStock", "data", "stockQuantity", "newStock", "Math", "max", "quantity", "update", "commit", "saveTransaction", "cartState", "paymentData", "attendantId", "_paymentData$notes", "transactionItems", "map", "_item$notes", "unitPrice", "totalPrice", "notes", "customerId", "paymentMethod", "rawTransactionData", "subtotal", "discount", "total", "status", "transactionData", "transactionId", "transaction", "transactionRef", "set", "customerRef", "get", "currentDebt"], "sources": ["E:/FX/Cyber POS/cyber-pos-system/src/hooks/useTransactions.ts"], "sourcesContent": ["import { useState } from 'react';\nimport {\n  collection,\n  doc,\n  addDoc,\n  getDoc,\n  serverTimestamp,\n  runTransaction,\n  writeBatch\n} from 'firebase/firestore';\nimport { db } from '../config/firebase';\nimport { TransactionItem, Customer, PaymentMethod } from '../types';\nimport { CartState } from './useCart';\nimport { cleanObject } from '../utils/objectUtils';\n\nexport const useTransactions = () => {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  // Create or update customer for credit sales\n  const createOrUpdateCustomer = async (customerName: string, customerPhone?: string): Promise<string> => {\n    try {\n      // Check if customer already exists by name\n      const customersRef = collection(db, 'customers');\n      const customerData: Omit<Customer, 'id'> = {\n        name: customerName.trim(),\n        phone: customerPhone?.trim(),\n        totalDebt: 0,\n        isActive: true,\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      };\n\n      // For simplicity, create a new customer document\n      // In production, you might want to search for existing customers first\n      const customerDoc = await addDoc(customersRef, {\n        ...customerData,\n        createdAt: serverTimestamp(),\n        updatedAt: serverTimestamp(),\n      });\n\n      return customerDoc.id;\n    } catch (error) {\n      console.error('Error creating customer:', error);\n      throw new Error('Failed to create customer record');\n    }\n  };\n\n  // Update product stock quantities\n  const updateProductStock = async (items: TransactionItem[]) => {\n    const batch = writeBatch(db);\n    \n    for (const item of items) {\n      if (item.type === 'product') {\n        const productRef = doc(db, 'products', item.itemId);\n        const productDoc = await getDoc(productRef);\n        \n        if (productDoc.exists()) {\n          const currentStock = productDoc.data().stockQuantity || 0;\n          const newStock = Math.max(0, currentStock - item.quantity);\n          \n          batch.update(productRef, {\n            stockQuantity: newStock,\n            updatedAt: serverTimestamp(),\n          });\n        }\n      }\n    }\n    \n    await batch.commit();\n  };\n\n  // Save transaction to Firebase\n  const saveTransaction = async (\n    cartState: CartState,\n    paymentData: {\n      paymentMethod: PaymentMethod;\n      customerName?: string;\n      customerPhone?: string;\n      notes?: string;\n    },\n    attendantId: string\n  ): Promise<string> => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      // Prepare transaction items - cleanObject will remove undefined values\n      const transactionItems: TransactionItem[] = cartState.items.map(item =>\n        cleanObject({\n          id: item.id,\n          type: item.type,\n          itemId: item.itemId,\n          name: item.name,\n          quantity: item.quantity,\n          unitPrice: item.unitPrice,\n          totalPrice: item.totalPrice,\n          notes: item.notes?.trim(),\n        })\n      );\n\n      // Handle customer creation for credit sales\n      let customerId: string | undefined;\n      if (paymentData.paymentMethod === 'debt' && paymentData.customerName) {\n        customerId = await createOrUpdateCustomer(paymentData.customerName, paymentData.customerPhone);\n      }\n\n      // Prepare transaction data - ensure no undefined values\n      const rawTransactionData = {\n        items: transactionItems,\n        subtotal: cartState.subtotal,\n        discount: cartState.discount || 0,\n        total: cartState.total,\n        paymentMethod: paymentData.paymentMethod,\n        customerId,\n        attendantId,\n        status: 'completed',\n        notes: paymentData.notes?.trim(),\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      };\n\n      // Clean the transaction data to remove any undefined values\n      const transactionData = cleanObject(rawTransactionData);\n\n      // Use Firestore transaction to ensure data consistency\n      const transactionId = await runTransaction(db, async (transaction) => {\n        // Create the transaction document\n        const transactionRef = doc(collection(db, 'transactions'));\n        transaction.set(transactionRef, {\n          ...transactionData,\n          createdAt: serverTimestamp(),\n          updatedAt: serverTimestamp(),\n        });\n\n        // Update customer debt if it's a credit sale\n        if (customerId && paymentData.paymentMethod === 'debt') {\n          const customerRef = doc(db, 'customers', customerId);\n          const customerDoc = await transaction.get(customerRef);\n          \n          if (customerDoc.exists()) {\n            const currentDebt = customerDoc.data().totalDebt || 0;\n            transaction.update(customerRef, {\n              totalDebt: currentDebt + cartState.total,\n              updatedAt: serverTimestamp(),\n            });\n          }\n        }\n\n        return transactionRef.id;\n      });\n\n      // Update product stock (outside of the transaction for better performance)\n      await updateProductStock(transactionItems);\n\n      return transactionId;\n    } catch (error) {\n      console.error('Error saving transaction:', error);\n      setError('Failed to save transaction');\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return {\n    saveTransaction,\n    loading,\n    error,\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SACEC,UAAU,EACVC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNC,eAAe,EACfC,cAAc,EACdC,UAAU,QACL,oBAAoB;AAC3B,SAASC,EAAE,QAAQ,oBAAoB;AAGvC,SAASC,WAAW,QAAQ,sBAAsB;AAElD,OAAO,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAgB,IAAI,CAAC;;EAEvD;EACA,MAAMgB,sBAAsB,GAAG,MAAAA,CAAOC,YAAoB,EAAEC,aAAsB,KAAsB;IACtG,IAAI;MACF;MACA,MAAMC,YAAY,GAAGlB,UAAU,CAACO,EAAE,EAAE,WAAW,CAAC;MAChD,MAAMY,YAAkC,GAAG;QACzCC,IAAI,EAAEJ,YAAY,CAACK,IAAI,CAAC,CAAC;QACzBC,KAAK,EAAEL,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEI,IAAI,CAAC,CAAC;QAC5BE,SAAS,EAAE,CAAC;QACZC,QAAQ,EAAE,IAAI;QACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBC,SAAS,EAAE,IAAID,IAAI,CAAC;MACtB,CAAC;;MAED;MACA;MACA,MAAME,WAAW,GAAG,MAAM1B,MAAM,CAACgB,YAAY,EAAE;QAC7C,GAAGC,YAAY;QACfM,SAAS,EAAErB,eAAe,CAAC,CAAC;QAC5BuB,SAAS,EAAEvB,eAAe,CAAC;MAC7B,CAAC,CAAC;MAEF,OAAOwB,WAAW,CAACC,EAAE;IACvB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAM,IAAIkB,KAAK,CAAC,kCAAkC,CAAC;IACrD;EACF,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAG,MAAOC,KAAwB,IAAK;IAC7D,MAAMC,KAAK,GAAG5B,UAAU,CAACC,EAAE,CAAC;IAE5B,KAAK,MAAM4B,IAAI,IAAIF,KAAK,EAAE;MACxB,IAAIE,IAAI,CAACC,IAAI,KAAK,SAAS,EAAE;QAC3B,MAAMC,UAAU,GAAGpC,GAAG,CAACM,EAAE,EAAE,UAAU,EAAE4B,IAAI,CAACG,MAAM,CAAC;QACnD,MAAMC,UAAU,GAAG,MAAMpC,MAAM,CAACkC,UAAU,CAAC;QAE3C,IAAIE,UAAU,CAACC,MAAM,CAAC,CAAC,EAAE;UACvB,MAAMC,YAAY,GAAGF,UAAU,CAACG,IAAI,CAAC,CAAC,CAACC,aAAa,IAAI,CAAC;UACzD,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEL,YAAY,GAAGN,IAAI,CAACY,QAAQ,CAAC;UAE1Db,KAAK,CAACc,MAAM,CAACX,UAAU,EAAE;YACvBM,aAAa,EAAEC,QAAQ;YACvBjB,SAAS,EAAEvB,eAAe,CAAC;UAC7B,CAAC,CAAC;QACJ;MACF;IACF;IAEA,MAAM8B,KAAK,CAACe,MAAM,CAAC,CAAC;EACtB,CAAC;;EAED;EACA,MAAMC,eAAe,GAAG,MAAAA,CACtBC,SAAoB,EACpBC,WAKC,EACDC,WAAmB,KACC;IACpBzC,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MAAA,IAAAwC,kBAAA;MACF;MACA,MAAMC,gBAAmC,GAAGJ,SAAS,CAAClB,KAAK,CAACuB,GAAG,CAACrB,IAAI;QAAA,IAAAsB,WAAA;QAAA,OAClEjD,WAAW,CAAC;UACVqB,EAAE,EAAEM,IAAI,CAACN,EAAE;UACXO,IAAI,EAAED,IAAI,CAACC,IAAI;UACfE,MAAM,EAAEH,IAAI,CAACG,MAAM;UACnBlB,IAAI,EAAEe,IAAI,CAACf,IAAI;UACf2B,QAAQ,EAAEZ,IAAI,CAACY,QAAQ;UACvBW,SAAS,EAAEvB,IAAI,CAACuB,SAAS;UACzBC,UAAU,EAAExB,IAAI,CAACwB,UAAU;UAC3BC,KAAK,GAAAH,WAAA,GAAEtB,IAAI,CAACyB,KAAK,cAAAH,WAAA,uBAAVA,WAAA,CAAYpC,IAAI,CAAC;QAC1B,CAAC,CAAC;MAAA,CACJ,CAAC;;MAED;MACA,IAAIwC,UAA8B;MAClC,IAAIT,WAAW,CAACU,aAAa,KAAK,MAAM,IAAIV,WAAW,CAACpC,YAAY,EAAE;QACpE6C,UAAU,GAAG,MAAM9C,sBAAsB,CAACqC,WAAW,CAACpC,YAAY,EAAEoC,WAAW,CAACnC,aAAa,CAAC;MAChG;;MAEA;MACA,MAAM8C,kBAAkB,GAAG;QACzB9B,KAAK,EAAEsB,gBAAgB;QACvBS,QAAQ,EAAEb,SAAS,CAACa,QAAQ;QAC5BC,QAAQ,EAAEd,SAAS,CAACc,QAAQ,IAAI,CAAC;QACjCC,KAAK,EAAEf,SAAS,CAACe,KAAK;QACtBJ,aAAa,EAAEV,WAAW,CAACU,aAAa;QACxCD,UAAU;QACVR,WAAW;QACXc,MAAM,EAAE,WAAW;QACnBP,KAAK,GAAAN,kBAAA,GAAEF,WAAW,CAACQ,KAAK,cAAAN,kBAAA,uBAAjBA,kBAAA,CAAmBjC,IAAI,CAAC,CAAC;QAChCI,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC;QACrBC,SAAS,EAAE,IAAID,IAAI,CAAC;MACtB,CAAC;;MAED;MACA,MAAM0C,eAAe,GAAG5D,WAAW,CAACuD,kBAAkB,CAAC;;MAEvD;MACA,MAAMM,aAAa,GAAG,MAAMhE,cAAc,CAACE,EAAE,EAAE,MAAO+D,WAAW,IAAK;QACpE;QACA,MAAMC,cAAc,GAAGtE,GAAG,CAACD,UAAU,CAACO,EAAE,EAAE,cAAc,CAAC,CAAC;QAC1D+D,WAAW,CAACE,GAAG,CAACD,cAAc,EAAE;UAC9B,GAAGH,eAAe;UAClB3C,SAAS,EAAErB,eAAe,CAAC,CAAC;UAC5BuB,SAAS,EAAEvB,eAAe,CAAC;QAC7B,CAAC,CAAC;;QAEF;QACA,IAAIyD,UAAU,IAAIT,WAAW,CAACU,aAAa,KAAK,MAAM,EAAE;UACtD,MAAMW,WAAW,GAAGxE,GAAG,CAACM,EAAE,EAAE,WAAW,EAAEsD,UAAU,CAAC;UACpD,MAAMjC,WAAW,GAAG,MAAM0C,WAAW,CAACI,GAAG,CAACD,WAAW,CAAC;UAEtD,IAAI7C,WAAW,CAACY,MAAM,CAAC,CAAC,EAAE;YACxB,MAAMmC,WAAW,GAAG/C,WAAW,CAACc,IAAI,CAAC,CAAC,CAACnB,SAAS,IAAI,CAAC;YACrD+C,WAAW,CAACtB,MAAM,CAACyB,WAAW,EAAE;cAC9BlD,SAAS,EAAEoD,WAAW,GAAGxB,SAAS,CAACe,KAAK;cACxCvC,SAAS,EAAEvB,eAAe,CAAC;YAC7B,CAAC,CAAC;UACJ;QACF;QAEA,OAAOmE,cAAc,CAAC1C,EAAE;MAC1B,CAAC,CAAC;;MAEF;MACA,MAAMG,kBAAkB,CAACuB,gBAAgB,CAAC;MAE1C,OAAOc,aAAa;IACtB,CAAC,CAAC,OAAOxD,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDC,QAAQ,CAAC,4BAA4B,CAAC;MACtC,MAAMD,KAAK;IACb,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,OAAO;IACLsC,eAAe;IACfvC,OAAO;IACPE;EACF,CAAC;AACH,CAAC;AAACH,EAAA,CA3JWD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}