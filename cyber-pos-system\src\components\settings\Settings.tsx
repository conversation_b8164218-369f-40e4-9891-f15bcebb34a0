import React, { useState, useEffect, useRef } from 'react';
import { Settings as SettingsIcon, Save, Users, Database, Upload, X, Building2 } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { useBusinessSettings } from '../../contexts/BusinessSettingsContext';
import UserManagement from '../users/UserManagement';
import { initializeDemoData } from '../../utils/seedData';
import { seedTestData } from '../../utils/testDataSeeder';
import { BusinessSettings } from '../../types';
import {
  getBusinessSettings,
  saveBusinessSettings,
  uploadLogo,
  deleteLogo,
  getDefaultBusinessSettings
} from '../../services/businessSettingsService';

const Settings: React.FC = () => {
  const { hasPermission, currentUser } = useAuth();
  const { refreshBusinessSettings } = useBusinessSettings();
  const [activeTab, setActiveTab] = useState('general');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  // Business settings state
  const [businessSettings, setBusinessSettings] = useState<BusinessSettings | null>(null);
  const [businessFormData, setBusinessFormData] = useState(getDefaultBusinessSettings());
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [savingSettings, setSavingSettings] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const tabs = [
    { id: 'general', name: 'Business Settings', icon: Building2 },
    ...(hasPermission('admin') ? [{ id: 'users', name: 'User Management', icon: Users }] : []),
    { id: 'data', name: 'Data Management', icon: Database },
  ];

  // Load business settings on component mount
  useEffect(() => {
    loadBusinessSettings();
  }, []);

  const loadBusinessSettings = async () => {
    try {
      const settings = await getBusinessSettings();
      if (settings) {
        setBusinessSettings(settings);
        setBusinessFormData({
          businessName: settings.businessName,
          address: settings.address,
          phone: settings.phone,
          email: settings.email || '',
          taxNumber: settings.taxNumber || '',
          licenseNumber: settings.licenseNumber || '',
          currency: settings.currency,
          taxRate: settings.taxRate,
          logoUrl: settings.logoUrl,
          logoFileName: settings.logoFileName,
          receiptHeader: settings.receiptHeader || '',
          receiptFooter: settings.receiptFooter,
          operatingHours: settings.operatingHours || '',
          website: settings.website || '',
        });
        if (settings.logoUrl) {
          setLogoPreview(settings.logoUrl);
        }
      }
    } catch (error) {
      console.error('Error loading business settings:', error);
      setMessage('Error loading business settings');
    }
  };

  const handleInitializeDemo = async () => {
    setLoading(true);
    setMessage('');

    try {
      await initializeDemoData();
      setMessage('Demo data initialized successfully!');
    } catch (error: any) {
      setMessage(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleSeedTestData = async () => {
    if (!currentUser) {
      setMessage('Error: User not authenticated');
      return;
    }

    setLoading(true);
    setMessage('');

    try {
      await seedTestData(currentUser.id);
      setMessage('Test data with transactions seeded successfully! You can now view reports with sample data.');
    } catch (error: any) {
      setMessage(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // Business settings handlers
  const handleBusinessFormChange = (field: string, value: string | number) => {
    setBusinessFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleLogoSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setLogoFile(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveLogo = () => {
    setLogoFile(null);
    setLogoPreview(null);
    setBusinessFormData(prev => ({
      ...prev,
      logoUrl: undefined,
      logoFileName: undefined
    }));
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSaveBusinessSettings = async () => {
    if (!hasPermission('admin')) {
      setMessage('Error: Admin permission required');
      return;
    }

    setSavingSettings(true);
    setMessage('');

    try {
      let logoUrl = businessFormData.logoUrl;
      let logoFileName = businessFormData.logoFileName;

      // Upload new logo if selected
      if (logoFile) {
        // Delete old logo if exists
        if (businessFormData.logoFileName) {
          await deleteLogo(businessFormData.logoFileName);
        }

        const uploadResult = await uploadLogo(logoFile);
        logoUrl = uploadResult.url;
        logoFileName = uploadResult.fileName;
      }

      const settingsToSave = {
        ...businessFormData,
        logoUrl,
        logoFileName,
      };

      const savedSettings = await saveBusinessSettings(settingsToSave);
      setBusinessSettings(savedSettings);
      setLogoFile(null);

      // Refresh the business settings context to update the UI
      await refreshBusinessSettings();

      setMessage('Business settings saved successfully!');
    } catch (error: any) {
      setMessage(`Error: ${error.message}`);
    } finally {
      setSavingSettings(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center">
            <SettingsIcon className="h-6 w-6 text-primary-600 mr-2" />
            <h1 className="text-2xl font-bold text-gray-900">System Settings</h1>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center`}
              >
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'general' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Business Settings</h3>

                {/* Logo Upload Section */}
                <div className="mb-8 p-6 bg-gray-50 rounded-lg">
                  <h4 className="text-md font-medium text-gray-900 mb-4">Company Logo</h4>
                  <div className="flex items-start space-x-6">
                    <div className="flex-shrink-0">
                      {logoPreview ? (
                        <div className="relative">
                          <img
                            src={logoPreview}
                            alt="Logo preview"
                            className="w-32 h-32 object-contain border border-gray-300 rounded-lg bg-white"
                          />
                          <button
                            onClick={handleRemoveLogo}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      ) : (
                        <div className="w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-white">
                          <Upload className="h-8 w-8 text-gray-400" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1">
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleLogoSelect}
                        className="hidden"
                      />
                      <button
                        onClick={() => fileInputRef.current?.click()}
                        className="bg-white border border-gray-300 rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
                      >
                        {logoPreview ? 'Change Logo' : 'Upload Logo'}
                      </button>
                      <p className="mt-2 text-sm text-gray-500">
                        Upload your company logo. Recommended size: 200x200px. Max file size: 5MB.
                        Supported formats: JPG, PNG, GIF.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Business Information Form */}
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Business Name *</label>
                    <input
                      type="text"
                      value={businessFormData.businessName}
                      onChange={(e) => handleBusinessFormChange('businessName', e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Phone Number *</label>
                    <input
                      type="tel"
                      value={businessFormData.phone}
                      onChange={(e) => handleBusinessFormChange('phone', e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      required
                    />
                  </div>
                  <div className="sm:col-span-2">
                    <label className="block text-sm font-medium text-gray-700">Business Address *</label>
                    <textarea
                      value={businessFormData.address}
                      onChange={(e) => handleBusinessFormChange('address', e.target.value)}
                      rows={3}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Email Address</label>
                    <input
                      type="email"
                      value={businessFormData.email}
                      onChange={(e) => handleBusinessFormChange('email', e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Website</label>
                    <input
                      type="url"
                      value={businessFormData.website}
                      onChange={(e) => handleBusinessFormChange('website', e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      placeholder="https://www.yourbusiness.com"
                    />
                  </div>
                </div>

                {/* Tax and Legal Information */}
                <div className="mt-8">
                  <h4 className="text-md font-medium text-gray-900 mb-4">Tax & Legal Information</h4>
                  <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Tax Registration Number (PIN)</label>
                      <input
                        type="text"
                        value={businessFormData.taxNumber}
                        onChange={(e) => handleBusinessFormChange('taxNumber', e.target.value)}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Business License Number</label>
                      <input
                        type="text"
                        value={businessFormData.licenseNumber}
                        onChange={(e) => handleBusinessFormChange('licenseNumber', e.target.value)}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Currency</label>
                      <select
                        value={businessFormData.currency}
                        onChange={(e) => handleBusinessFormChange('currency', e.target.value)}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      >
                        <option value="KSh">KSh (Kenyan Shilling)</option>
                        <option value="USD">USD (US Dollar)</option>
                        <option value="EUR">EUR (Euro)</option>
                        <option value="GBP">GBP (British Pound)</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Tax Rate (%)</label>
                      <input
                        type="number"
                        value={businessFormData.taxRate}
                        onChange={(e) => handleBusinessFormChange('taxRate', parseFloat(e.target.value) || 0)}
                        step="0.01"
                        min="0"
                        max="100"
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                      />
                    </div>
                  </div>
                </div>

                {/* Receipt Configuration */}
                <div className="mt-8">
                  <h4 className="text-md font-medium text-gray-900 mb-4">Receipt Configuration</h4>
                  <div className="grid grid-cols-1 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Receipt Header (Optional)</label>
                      <input
                        type="text"
                        value={businessFormData.receiptHeader}
                        onChange={(e) => handleBusinessFormChange('receiptHeader', e.target.value)}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                        placeholder="Special message for receipt header"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Receipt Footer</label>
                      <textarea
                        value={businessFormData.receiptFooter}
                        onChange={(e) => handleBusinessFormChange('receiptFooter', e.target.value)}
                        rows={3}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                        placeholder="Thank you message and additional information"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Operating Hours</label>
                      <textarea
                        value={businessFormData.operatingHours}
                        onChange={(e) => handleBusinessFormChange('operatingHours', e.target.value)}
                        rows={2}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                        placeholder="Mon-Fri: 8:00 AM - 6:00 PM"
                      />
                    </div>
                  </div>
                </div>

                {/* Save Button */}
                <div className="mt-8 pt-6 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-500">
                      * Required fields
                    </div>
                    <button
                      onClick={handleSaveBusinessSettings}
                      disabled={savingSettings || !hasPermission('admin')}
                      className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      {savingSettings ? 'Saving...' : 'Save Business Settings'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'users' && hasPermission('admin') && (
            <UserManagement />
          )}

          {activeTab === 'data' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Data Management</h3>
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <Database className="h-5 w-5 text-yellow-400" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-yellow-800">
                        Demo Data Initialization
                      </h3>
                      <div className="mt-2 text-sm text-yellow-700">
                        <p>
                          Initialize the system with demo services and products for testing purposes.
                          This will add sample cyber services and stationery items to your database.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {message && (
                  <div className={`p-4 rounded-md mb-4 ${
                    message.includes('Error')
                      ? 'bg-red-50 text-red-700 border border-red-200'
                      : 'bg-green-50 text-green-700 border border-green-200'
                  }`}>
                    {message}
                  </div>
                )}

                <div className="space-y-4">
                  <button
                    onClick={handleInitializeDemo}
                    disabled={loading}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50 mr-4"
                  >
                    <Database className="h-4 w-4 mr-2" />
                    {loading ? 'Initializing...' : 'Initialize Demo Data'}
                  </button>

                  <button
                    onClick={handleSeedTestData}
                    disabled={loading}
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"
                  >
                    <Database className="h-4 w-4 mr-2" />
                    {loading ? 'Seeding...' : 'Seed Test Data with Transactions'}
                  </button>

                  <div className="text-sm text-gray-600 mt-2">
                    <p><strong>Initialize Demo Data:</strong> Adds basic services and products</p>
                    <p><strong>Seed Test Data:</strong> Adds comprehensive test data including 30 days of sample transactions for testing reports</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Settings;
