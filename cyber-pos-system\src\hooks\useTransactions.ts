import { useState } from 'react';
import {
  collection,
  doc,
  addDoc,
  getDoc,
  serverTimestamp,
  runTransaction,
  writeBatch
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { TransactionItem, Customer, PaymentMethod } from '../types';
import { CartState } from './useCart';
import { cleanObject } from '../utils/objectUtils';

export const useTransactions = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Create or update customer for credit sales
  const createOrUpdateCustomer = async (customerName: string, customerPhone?: string): Promise<string> => {
    try {
      // Check if customer already exists by name
      const customersRef = collection(db, 'customers');
      const customerData: Omit<Customer, 'id'> = {
        name: customerName.trim(),
        phone: customerPhone?.trim(),
        totalDebt: 0,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // For simplicity, create a new customer document
      // In production, you might want to search for existing customers first
      const customerDoc = await addDoc(customersRef, {
        ...customerData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });

      return customerDoc.id;
    } catch (error) {
      console.error('Error creating customer:', error);
      throw new Error('Failed to create customer record');
    }
  };

  // Update product stock quantities
  const updateProductStock = async (items: TransactionItem[]) => {
    const batch = writeBatch(db);
    
    for (const item of items) {
      if (item.type === 'product') {
        const productRef = doc(db, 'products', item.itemId);
        const productDoc = await getDoc(productRef);
        
        if (productDoc.exists()) {
          const currentStock = productDoc.data().stockQuantity || 0;
          const newStock = Math.max(0, currentStock - item.quantity);
          
          batch.update(productRef, {
            stockQuantity: newStock,
            updatedAt: serverTimestamp(),
          });
        }
      }
    }
    
    await batch.commit();
  };

  // Save transaction to Firebase
  const saveTransaction = async (
    cartState: CartState,
    paymentData: {
      paymentMethod: PaymentMethod;
      customerName?: string;
      customerPhone?: string;
      notes?: string;
    },
    attendantId: string
  ): Promise<string> => {
    setLoading(true);
    setError(null);

    try {
      // Prepare transaction items - cleanObject will remove undefined values
      const transactionItems: TransactionItem[] = cartState.items.map(item =>
        cleanObject({
          id: item.id,
          type: item.type,
          itemId: item.itemId,
          name: item.name,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: item.totalPrice,
          notes: item.notes?.trim(),
        })
      );

      // Handle customer creation for credit sales
      let customerId: string | undefined;
      if (paymentData.paymentMethod === 'debt' && paymentData.customerName) {
        customerId = await createOrUpdateCustomer(paymentData.customerName, paymentData.customerPhone);
      }

      // Prepare transaction data - ensure no undefined values
      const rawTransactionData = {
        items: transactionItems,
        subtotal: cartState.subtotal,
        discount: cartState.discount || 0,
        total: cartState.total,
        paymentMethod: paymentData.paymentMethod,
        customerId,
        attendantId,
        status: 'completed',
        notes: paymentData.notes?.trim(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Clean the transaction data to remove any undefined values
      const transactionData = cleanObject(rawTransactionData);

      // Use Firestore transaction to ensure data consistency
      const transactionId = await runTransaction(db, async (transaction) => {
        // Create the transaction document
        const transactionRef = doc(collection(db, 'transactions'));
        transaction.set(transactionRef, {
          ...transactionData,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        });

        // Update customer debt if it's a credit sale
        if (customerId && paymentData.paymentMethod === 'debt') {
          const customerRef = doc(db, 'customers', customerId);
          const customerDoc = await transaction.get(customerRef);
          
          if (customerDoc.exists()) {
            const currentDebt = customerDoc.data().totalDebt || 0;
            transaction.update(customerRef, {
              totalDebt: currentDebt + cartState.total,
              updatedAt: serverTimestamp(),
            });
          }
        }

        return transactionRef.id;
      });

      // Update product stock (outside of the transaction for better performance)
      await updateProductStock(transactionItems);

      return transactionId;
    } catch (error) {
      console.error('Error saving transaction:', error);
      setError('Failed to save transaction');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return {
    saveTransaction,
    loading,
    error,
  };
};
