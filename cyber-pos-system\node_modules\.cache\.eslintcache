[{"E:\\FX\\Cyber POS\\cyber-pos-system\\src\\index.tsx": "1", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\reportWebVitals.js": "2", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\App.tsx": "3", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\AuthContext.tsx": "4", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\dashboard\\Dashboard.tsx": "5", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\auth\\Login.tsx": "6", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\Inventory.tsx": "7", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POS.tsx": "8", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\Services.tsx": "9", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\Reports.tsx": "10", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\settings\\Settings.tsx": "11", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\layout\\Layout.tsx": "12", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\common\\LoadingSpinner.tsx": "13", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\offline\\OfflineManager.tsx": "14", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\config\\firebase.ts": "15", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useDashboard.ts": "16", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useProducts.ts": "17", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useServices.ts": "18", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useReports.ts": "19", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useCart.ts": "20", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\seedData.ts": "21", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\testDataSeeder.ts": "22", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\InventoryStats.tsx": "23", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\ProductModal.tsx": "24", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\StockAdjustmentModal.tsx": "25", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\LowStockAlert.tsx": "26", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POSCart.tsx": "27", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ServiceSelector.tsx": "28", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ProductSelector.tsx": "29", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceCategories.tsx": "30", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceStats.tsx": "31", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceModal.tsx": "32", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\InventoryAnalytics.tsx": "33", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\users\\UserManagement.tsx": "34", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useTransactions.ts": "35", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\receiptGenerator.ts": "36", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\serviceUtils.ts": "37", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\CheckoutModal.tsx": "38", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\BulkOperations.tsx": "39", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\excelImport.ts": "40", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\debug\\FirebaseConnectionTest.tsx": "41", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\firebaseConnectionTest.ts": "42", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\objectUtils.ts": "43"}, {"size": 550, "mtime": 1751002257124, "results": "44", "hashOfConfig": "45"}, {"size": 362, "mtime": 1751001516282, "results": "46", "hashOfConfig": "45"}, {"size": 2473, "mtime": 1751092380796, "results": "47", "hashOfConfig": "45"}, {"size": 5912, "mtime": 1751002889593, "results": "48", "hashOfConfig": "45"}, {"size": 9398, "mtime": 1751013933072, "results": "49", "hashOfConfig": "45"}, {"size": 7363, "mtime": 1751020900684, "results": "50", "hashOfConfig": "45"}, {"size": 17601, "mtime": 1751093334719, "results": "51", "hashOfConfig": "45"}, {"size": 8045, "mtime": 1751003816943, "results": "52", "hashOfConfig": "45"}, {"size": 13480, "mtime": 1751003649429, "results": "53", "hashOfConfig": "45"}, {"size": 26580, "mtime": 1751008212115, "results": "54", "hashOfConfig": "45"}, {"size": 8562, "mtime": 1751007249570, "results": "55", "hashOfConfig": "45"}, {"size": 6483, "mtime": 1751001825178, "results": "56", "hashOfConfig": "45"}, {"size": 665, "mtime": 1751001779724, "results": "57", "hashOfConfig": "45"}, {"size": 10403, "mtime": 1751017441838, "results": "58", "hashOfConfig": "45"}, {"size": 3887, "mtime": 1751019446562, "results": "59", "hashOfConfig": "45"}, {"size": 9454, "mtime": 1751013836375, "results": "60", "hashOfConfig": "45"}, {"size": 7430, "mtime": 1751098211368, "results": "61", "hashOfConfig": "45"}, {"size": 5279, "mtime": 1751092620848, "results": "62", "hashOfConfig": "45"}, {"size": 10667, "mtime": 1751006877684, "results": "63", "hashOfConfig": "45"}, {"size": 5107, "mtime": 1751003753867, "results": "64", "hashOfConfig": "45"}, {"size": 9579, "mtime": 1751020844856, "results": "65", "hashOfConfig": "45"}, {"size": 7956, "mtime": 1751007184267, "results": "66", "hashOfConfig": "45"}, {"size": 11510, "mtime": 1751009626258, "results": "67", "hashOfConfig": "45"}, {"size": 12807, "mtime": 1751094204600, "results": "68", "hashOfConfig": "45"}, {"size": 10775, "mtime": 1751004342324, "results": "69", "hashOfConfig": "45"}, {"size": 12479, "mtime": 1751004391960, "results": "70", "hashOfConfig": "45"}, {"size": 15195, "mtime": 1751005588991, "results": "71", "hashOfConfig": "45"}, {"size": 13401, "mtime": 1751003915007, "results": "72", "hashOfConfig": "45"}, {"size": 10910, "mtime": 1751003957303, "results": "73", "hashOfConfig": "45"}, {"size": 3964, "mtime": 1751003442458, "results": "74", "hashOfConfig": "45"}, {"size": 8498, "mtime": 1751003577995, "results": "75", "hashOfConfig": "45"}, {"size": 9054, "mtime": 1751003411814, "results": "76", "hashOfConfig": "45"}, {"size": 11145, "mtime": 1751009654679, "results": "77", "hashOfConfig": "45"}, {"size": 12716, "mtime": 1751002934527, "results": "78", "hashOfConfig": "45"}, {"size": 5339, "mtime": 1751099463810, "results": "79", "hashOfConfig": "45"}, {"size": 7154, "mtime": 1751017196047, "results": "80", "hashOfConfig": "45"}, {"size": 5162, "mtime": 1751003518513, "results": "81", "hashOfConfig": "45"}, {"size": 11914, "mtime": 1751004034012, "results": "82", "hashOfConfig": "45"}, {"size": 26303, "mtime": 1751093657648, "results": "83", "hashOfConfig": "45"}, {"size": 13824, "mtime": 1751026107868, "results": "84", "hashOfConfig": "45"}, {"size": 3407, "mtime": 1751030363309, "results": "85", "hashOfConfig": "45"}, {"size": 2725, "mtime": 1751030347462, "results": "86", "hashOfConfig": "45"}, {"size": 729, "mtime": 1751098798516, "results": "87", "hashOfConfig": "45"}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "do8dzb", {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\index.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\reportWebVitals.js", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\App.tsx", ["217"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\AuthContext.tsx", ["218"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\dashboard\\Dashboard.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\auth\\Login.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\Inventory.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POS.tsx", ["219", "220", "221", "222", "223", "224", "225", "226"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\Services.tsx", ["227", "228", "229", "230"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\Reports.tsx", ["231", "232", "233"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\settings\\Settings.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\layout\\Layout.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\common\\LoadingSpinner.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\offline\\OfflineManager.tsx", ["234", "235", "236"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\config\\firebase.ts", ["237"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useDashboard.ts", ["238", "239"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useProducts.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useServices.ts", ["240", "241"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useReports.ts", ["242", "243", "244", "245", "246", "247"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useCart.ts", ["248", "249"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\seedData.ts", ["250", "251", "252"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\testDataSeeder.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\InventoryStats.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\ProductModal.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\StockAdjustmentModal.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\LowStockAlert.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POSCart.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ServiceSelector.tsx", ["253"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ProductSelector.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceCategories.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceStats.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceModal.tsx", ["254"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\InventoryAnalytics.tsx", ["255", "256"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\users\\UserManagement.tsx", ["257", "258"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useTransactions.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\receiptGenerator.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\serviceUtils.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\CheckoutModal.tsx", ["259"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\BulkOperations.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\excelImport.ts", ["260"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\debug\\FirebaseConnectionTest.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\firebaseConnectionTest.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\objectUtils.ts", [], [], {"ruleId": "261", "severity": 1, "message": "262", "line": 18, "column": 7, "nodeType": "263", "messageId": "264", "endLine": 18, "endColumn": 62}, {"ruleId": "261", "severity": 1, "message": "265", "line": 16, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 16, "endColumn": 8}, {"ruleId": "261", "severity": 1, "message": "266", "line": 10, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 10, "endColumn": 7}, {"ruleId": "261", "severity": 1, "message": "267", "line": 11, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 11, "endColumn": 13}, {"ruleId": "261", "severity": 1, "message": "268", "line": 12, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 12, "endColumn": 10}, {"ruleId": "261", "severity": 1, "message": "269", "line": 13, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 13, "endColumn": 9}, {"ruleId": "261", "severity": 1, "message": "270", "line": 14, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 14, "endColumn": 7}, {"ruleId": "261", "severity": 1, "message": "271", "line": 15, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 15, "endColumn": 8}, {"ruleId": "261", "severity": 1, "message": "272", "line": 21, "column": 10, "nodeType": "263", "messageId": "264", "endLine": 21, "endColumn": 17}, {"ruleId": "261", "severity": 1, "message": "273", "line": 21, "column": 19, "nodeType": "263", "messageId": "264", "endLine": 21, "endColumn": 26}, {"ruleId": "261", "severity": 1, "message": "274", "line": 9, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 9, "endColumn": 13}, {"ruleId": "261", "severity": 1, "message": "275", "line": 10, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 10, "endColumn": 6}, {"ruleId": "261", "severity": 1, "message": "276", "line": 14, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 14, "endColumn": 4}, {"ruleId": "261", "severity": 1, "message": "277", "line": 15, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 15, "endColumn": 7}, {"ruleId": "261", "severity": 1, "message": "278", "line": 11, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 11, "endColumn": 9}, {"ruleId": "261", "severity": 1, "message": "279", "line": 26, "column": 10, "nodeType": "263", "messageId": "264", "endLine": 26, "endColumn": 13}, {"ruleId": "280", "severity": 1, "message": "281", "line": 57, "column": 6, "nodeType": "282", "endLine": 57, "endColumn": 22, "suggestions": "283"}, {"ruleId": "261", "severity": 1, "message": "284", "line": 6, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 6, "endColumn": 14}, {"ruleId": "261", "severity": 1, "message": "285", "line": 7, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 7, "endColumn": 14}, {"ruleId": "280", "severity": 1, "message": "286", "line": 51, "column": 6, "nodeType": "282", "endLine": 51, "endColumn": 27, "suggestions": "287"}, {"ruleId": "261", "severity": 1, "message": "288", "line": 8, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 8, "endColumn": 23}, {"ruleId": "261", "severity": 1, "message": "273", "line": 12, "column": 23, "nodeType": "263", "messageId": "264", "endLine": 12, "endColumn": 30}, {"ruleId": "280", "severity": 1, "message": "289", "line": 300, "column": 6, "nodeType": "282", "endLine": 300, "endColumn": 8, "suggestions": "290"}, {"ruleId": "261", "severity": 1, "message": "291", "line": 5, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 5, "endColumn": 10}, {"ruleId": "261", "severity": 1, "message": "265", "line": 11, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 11, "endColumn": 8}, {"ruleId": "261", "severity": 1, "message": "292", "line": 1, "column": 20, "nodeType": "263", "messageId": "264", "endLine": 1, "endColumn": 29}, {"ruleId": "261", "severity": 1, "message": "293", "line": 9, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 9, "endColumn": 13}, {"ruleId": "261", "severity": 1, "message": "294", "line": 10, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 10, "endColumn": 8}, {"ruleId": "261", "severity": 1, "message": "273", "line": 13, "column": 23, "nodeType": "263", "messageId": "264", "endLine": 13, "endColumn": 30}, {"ruleId": "261", "severity": 1, "message": "272", "line": 13, "column": 32, "nodeType": "263", "messageId": "264", "endLine": 13, "endColumn": 39}, {"ruleId": "261", "severity": 1, "message": "266", "line": 13, "column": 41, "nodeType": "263", "messageId": "264", "endLine": 13, "endColumn": 45}, {"ruleId": "261", "severity": 1, "message": "295", "line": 3, "column": 10, "nodeType": "263", "messageId": "264", "endLine": 3, "endColumn": 31}, {"ruleId": "280", "severity": 1, "message": "296", "line": 87, "column": 6, "nodeType": "282", "endLine": 87, "endColumn": 8, "suggestions": "297"}, {"ruleId": "261", "severity": 1, "message": "266", "line": 14, "column": 10, "nodeType": "263", "messageId": "264", "endLine": 14, "endColumn": 14}, {"ruleId": "261", "severity": 1, "message": "272", "line": 14, "column": 16, "nodeType": "263", "messageId": "264", "endLine": 14, "endColumn": 23}, {"ruleId": "261", "severity": 1, "message": "273", "line": 14, "column": 25, "nodeType": "263", "messageId": "264", "endLine": 14, "endColumn": 32}, {"ruleId": "261", "severity": 1, "message": "274", "line": 5, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 5, "endColumn": 13}, {"ruleId": "261", "severity": 1, "message": "275", "line": 2, "column": 25, "nodeType": "263", "messageId": "264", "endLine": 2, "endColumn": 28}, {"ruleId": "261", "severity": 1, "message": "273", "line": 12, "column": 10, "nodeType": "263", "messageId": "264", "endLine": 12, "endColumn": 17}, {"ruleId": "280", "severity": 1, "message": "298", "line": 45, "column": 6, "nodeType": "282", "endLine": 45, "endColumn": 16, "suggestions": "299"}, {"ruleId": "261", "severity": 1, "message": "269", "line": 8, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 8, "endColumn": 9}, {"ruleId": "280", "severity": 1, "message": "300", "line": 34, "column": 6, "nodeType": "282", "endLine": 34, "endColumn": 8, "suggestions": "301"}, {"ruleId": "261", "severity": 1, "message": "302", "line": 9, "column": 3, "nodeType": "263", "messageId": "264", "endLine": 9, "endColumn": 10}, {"ruleId": "261", "severity": 1, "message": "303", "line": 21, "column": 7, "nodeType": "263", "messageId": "264", "endLine": 21, "endColumn": 23}, "@typescript-eslint/no-unused-vars", "'ProtectedRoute' is assigned a value but never used.", "Identifier", "unusedVar", "'where' is defined but never used.", "'User' is defined but never used.", "'CreditCard' is defined but never used.", "'Receipt' is defined but never used.", "'Trash2' is defined but never used.", "'Plus' is defined but never used.", "'Minus' is defined but never used.", "'Service' is defined but never used.", "'Product' is defined but never used.", "'DollarSign' is defined but never used.", "'Tag' is defined but never used.", "'X' is defined but never used.", "'Save' is defined but never used.", "'Filter' is defined but never used.", "'Bar' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadReport'. Either include it or remove the dependency array.", "ArrayExpression", ["304"], "'AlertCircle' is defined but never used.", "'CheckCircle' is defined but never used.", "React Hook useEffect has a missing dependency: 'syncOfflineData'. Either include it or remove the dependency array.", ["305"], "'persistentLocalCache' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", ["306"], "'getDocs' is defined but never used.", "'useEffect' is defined but never used.", "'startAfter' is defined but never used.", "'limit' is defined but never used.", "'calculateServicePrice' is defined but never used.", "React Hook useCallback has a missing dependency: 'removeFromCart'. Either include it or remove the dependency array.", ["307"], "React Hook useEffect has missing dependencies: 'calculateMetrics' and 'generateAlerts'. Either include them or remove the dependency array.", ["308"], "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", ["309"], "'Printer' is defined but never used.", "'OPTIONAL_COLUMNS' is assigned a value but never used.", {"desc": "310", "fix": "311"}, {"desc": "312", "fix": "313"}, {"desc": "314", "fix": "315"}, {"desc": "316", "fix": "317"}, {"desc": "318", "fix": "319"}, {"desc": "320", "fix": "321"}, "Update the dependencies array to be: [loadReport, selectedPeriod]", {"range": "322", "text": "323"}, "Update the dependencies array to be: [offlineQueue.length, syncOfflineData]", {"range": "324", "text": "325"}, "Update the dependencies array to be: [loadDashboardData]", {"range": "326", "text": "327"}, "Update the dependencies array to be: [removeFromCart]", {"range": "328", "text": "329"}, "Update the dependencies array to be: [calculateMetrics, generateAlerts, products]", {"range": "330", "text": "331"}, "Update the dependencies array to be: [loadUsers]", {"range": "332", "text": "333"}, [1381, 1397], "[load<PERSON><PERSON><PERSON>, selected<PERSON><PERSON><PERSON>]", [1357, 1378], "[offlineQueue.length, syncOfflineData]", [9246, 9248], "[loadDashboardData]", [2571, 2573], "[remove<PERSON><PERSON><PERSON><PERSON>]", [1163, 1173], "[calculateMetrics, generateAlerts, products]", [865, 867], "[loadUsers]"]